package com.kbao.kbcelms.enterprise.type.bean;

import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业类型展示VO
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(value = "EnterpriseTypeVO", description = "企业类型展示VO")
public class EnterpriseTypeVO extends EnterpriseType {

    @ApiModelProperty(value = "员工规模范围展示文本")
    private String employeeRangeText;

    @ApiModelProperty(value = "营收规模范围展示文本")
    private String revenueRangeText;

    /**
     * 获取格式化的规则显示列表（后端组装）
     */
    public List<String> getRuleDisplayList() {
        if (getRules() == null || getRules().isEmpty()) {
            return Arrays.asList("从业人员：无限制", "营业收入：无限制");
        }

        return getRules().stream()
                   .map(EnterpriseTypeRule::getRuleDisplay)
                   .collect(Collectors.toList());
    }

    /**
     * 获取规则摘要（用于列表显示）
     */
    public String getRuleSummary() {
        List<String> displays = getRuleDisplayList();
        return String.join("；", displays);
    }
}
