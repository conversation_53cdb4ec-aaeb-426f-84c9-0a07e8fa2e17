package com.kbao.kbcelms.enterprise.type.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 企业类型判定规则
 * <AUTHOR>
 * @date 2025-07-28
 */
@Data
@ApiModel(value = "EnterpriseTypeRule", description = "企业类型判定规则")
public class EnterpriseTypeRule implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @ApiModelProperty(value = "规则字段：employeeCount-员工规模，revenue-营收规模")
    private String field;

    @ApiModelProperty(value = "字段显示名：从业人员、营业收入")
    private String fieldName;

    @ApiModelProperty(value = "单位：人、万元、亿元")
    private String unit;

    @ApiModelProperty(value = "最小值（可为null）")
    private Double minValue;

    @ApiModelProperty(value = "最大值（可为null）")
    private Double maxValue;

    /**
     * 获取规则显示文本（后端组装）
     * @return 格式化的规则文本，如：">= 100万元 < 300万元"
     */
    public String getRuleDisplay() {
        if (minValue == null && maxValue == null) {
            return fieldName + "：无限制";
        }

        StringBuilder display = new StringBuilder(fieldName + "：");

        if (minValue != null && maxValue != null) {
            // 双边界：>= 100万元 < 300万元
            display.append(">= ").append(formatValue(minValue, unit))
                   .append(" < ").append(formatValue(maxValue, unit));
        } else if (minValue != null) {
            // 只有下界：>= 100万元
            display.append(">= ").append(formatValue(minValue, unit));
        } else {
            // 只有上界：< 300万元
            display.append("< ").append(formatValue(maxValue, unit));
        }

        return display.toString();
    }

    /**
     * 格式化数值显示
     */
    private String formatValue(Double value, String unit) {
        if (value == null) return "";

        if ("亿元".equals(unit)) {
            return value + "亿元";
        } else if ("万元".equals(unit)) {
            return value + "万元";
        } else {
            return value + unit;
        }
    }
}
