package com.kbao.kbcelms.enterprise.base.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kbao.commons.exception.BusinessException;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBeneficiary;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseFinancial;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseShareholder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 天眼查API对接服务
 * <AUTHOR>
 * @date 2025-07-31
 */
@Service
@Slf4j
public class TianyanchaService {
    
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    
    @Autowired
    private EnterpriseShareholderService enterpriseShareholderService;
    
    @Autowired
    private EnterpriseBeneficiaryService enterpriseBeneficiaryService;
    
    @Autowired
    private EnterpriseFinancialService enterpriseFinancialService;

    @Autowired(required = false)
    private RestTemplate restTemplate;
    
    @Value("${tianyancha.api.baseUrl:https://open.api.tianyancha.com}")
    private String apiBaseUrl;
    
    @Value("${tianyancha.api.token:}")
    private String apiToken;
    
    /**
     * 同步企业所有信息
     * @param companyName 企业名称
     * @return 同步结果
     */
    public EnterpriseBasicInfo syncEnterpriseAllInfo(String companyName) {
        if (!StringUtils.hasText(companyName)) {
            throw new BusinessException("企业名称不能为空");
        }
        try {
            // 1. 先同步企业基本信息，获取统一社会信用代码
            EnterpriseBasicInfo basicInfo = syncEnterpriseBasicInfo(companyName);
            if (basicInfo == null) {
                log.warn("未找到企业信息，companyName: {}", companyName);
                return null;
            }
            String creditCode = basicInfo.getCreditCode();
            // 2. 同步企业股东信息
            syncEnterpriseShareholder(creditCode);

            // 3. 同步最终受益人信息
            syncEnterpriseBeneficiary(creditCode);

            // 4. 同步财务信息（仅上市公司）
            syncEnterpriseFinancial(creditCode);

            return basicInfo;
        } catch (Exception e) {
            log.error("同步企业信息失败，companyName: {}, error: {}", companyName, e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 同步企业基本信息
     * @param companyName 企业名称
     * @return 统一社会信用代码
     */
    public EnterpriseBasicInfo syncEnterpriseBasicInfo(String companyName) {
        try {
            // 调用818接口获取企业基本信息
            JSONObject basicInfoResult = callTianyanchaApi("/services/open/ic/baseinfoV2/2.0", companyName);

            if (basicInfoResult != null && basicInfoResult.getInteger("error_code") == 0) {
                JSONObject result = basicInfoResult.getJSONObject("result");

                // 获取统一社会信用代码
                String creditCode = result.getString("creditCode");
                if (!StringUtils.hasText(creditCode)) {
                    log.warn("企业基本信息中未包含统一社会信用代码，企业名称: {}", companyName);
                    return null;
                }

                EnterpriseBasicInfo basicInfo = new EnterpriseBasicInfo();
                basicInfo.setCreditCode(creditCode);

                // 根据文档字段映射
                basicInfo.setHistoryNames(result.getString("historyNames"));
                basicInfo.setRegStatus(result.getString("regStatus"));
                basicInfo.setEmailList(result.getJSONArray("emailList") != null ?
                    result.getJSONArray("emailList").toJavaList(String.class) : null);
                basicInfo.setBondNum(result.getString("bondNum"));
                basicInfo.setBondName(result.getString("bondName"));
                basicInfo.setType(result.getInteger("type"));
                basicInfo.setRevokeReason(result.getString("revokeReason"));
                basicInfo.setProperty3(result.getString("property3"));
                basicInfo.setUsedBondName(result.getString("usedBondName"));
                basicInfo.setApprovedTime(result.getLong("approvedTime"));
                basicInfo.setCompanyId(result.getLong("id"));
                basicInfo.setOrgNumber(result.getString("orgNumber"));
                basicInfo.setBusinessScope(result.getString("businessScope"));
                basicInfo.setTaxNumber(result.getString("taxNumber"));
                basicInfo.setRegCapitalCurrency(result.getString("regCapitalCurrency"));
                basicInfo.setTags(result.getString("tags"));
                basicInfo.setPhoneNumber(result.getString("phoneNumber"));
                basicInfo.setDistrict(result.getString("district"));
                basicInfo.setEconomicFunctionZone1(result.getString("economicFunctionZone1"));
                basicInfo.setEconomicFunctionZone2(result.getString("economicFunctionZone2"));
                basicInfo.setName(result.getString("name"));
                basicInfo.setPercentileScore(result.getInteger("percentileScore"));

                // 处理行业信息
                JSONObject industryAllObj = result.getJSONObject("industryAll");
                if (industryAllObj != null) {
                    EnterpriseBasicInfo.IndustryAll industryAll = new EnterpriseBasicInfo.IndustryAll();
                    industryAll.setCategory(industryAllObj.getString("category"));
                    industryAll.setCategoryMiddle(industryAllObj.getString("categoryMiddle"));
                    industryAll.setCategoryBig(industryAllObj.getString("categoryBig"));
                    industryAll.setCategorySmall(industryAllObj.getString("categorySmall"));
                    industryAll.setCategoryCodeFirst(industryAllObj.getString("categoryCodeFirst"));
                    industryAll.setCategoryCodeSecond(industryAllObj.getString("categoryCodeSecond"));
                    industryAll.setCategoryCodeThird(industryAllObj.getString("categoryCodeThird"));
                    industryAll.setCategoryCodeFourth(industryAllObj.getString("categoryCodeFourth"));
                    basicInfo.setIndustryAll(industryAll);
                }

                basicInfo.setIsMicroEnt(result.getInteger("isMicroEnt"));
                basicInfo.setCancelDate(result.getLong("cancelDate"));
                basicInfo.setDistrictCode(result.getString("districtCode"));
                basicInfo.setRegCapital(result.getString("regCapital"));
                basicInfo.setCity(result.getString("city"));
                basicInfo.setStaffNumRange(result.getString("staffNumRange"));
                basicInfo.setHistoryNameList(result.getJSONArray("historyNameList") != null ?
                    result.getJSONArray("historyNameList").toJavaList(String.class) : null);
                basicInfo.setIndustry(result.getString("industry"));
                basicInfo.setRevokeDate(result.getLong("revokeDate"));
                basicInfo.setUpdateTimes(result.getLong("updateTimes"));
                basicInfo.setBRNNumber(result.getString("BRNNumber"));
                basicInfo.setLegalPersonName(result.getString("legalPersonName"));
                basicInfo.setRegNumber(result.getString("regNumber"));
                basicInfo.setFromTime(result.getLong("fromTime"));
                basicInfo.setSocialStaffNum(result.getInteger("socialStaffNum"));
                basicInfo.setActualCapitalCurrency(result.getString("actualCapitalCurrency"));
                basicInfo.setAlias(result.getString("alias"));
                basicInfo.setCompanyOrgType(result.getString("companyOrgType"));
                basicInfo.setCancelReason(result.getString("cancelReason"));
                basicInfo.setToTime(result.getLong("toTime"));
                basicInfo.setEmail(result.getString("email"));
                basicInfo.setActualCapital(result.getString("actualCapital"));
                basicInfo.setEstablishTime(result.getLong("establishTime"));
                basicInfo.setRegInstitute(result.getString("regInstitute"));
                basicInfo.setRegLocation(result.getString("regLocation"));
                basicInfo.setWebsiteList(result.getString("websiteList"));
                basicInfo.setBondType(result.getString("bondType"));
                basicInfo.setBase(result.getString("base"));

                // 同步企业规模信息
                syncEnterpriseScale(creditCode, basicInfo);

                enterpriseBasicInfoService.saveOrUpdate(basicInfo);
                log.info("企业基本信息同步成功，creditCode: {}", creditCode);

                return basicInfo;
            } else {
                log.error("获取企业基本信息失败，companyName: {}, result: {}", companyName, basicInfoResult);
                return null;
            }
        } catch (Exception e) {
            log.error("同步企业基本信息失败，companyName: {}, error: {}", companyName, e.getMessage(), e);
            throw new BusinessException("同步企业基本信息失败：" + e.getMessage());
        }
    }

    /**
     * 同步企业规模信息（内部方法）
     * @param creditCode 统一社会信用代码
     * @param basicInfo 企业基本信息对象
     */
    private void syncEnterpriseScale(String creditCode, EnterpriseBasicInfo basicInfo) {
        try {
            // 调用1149接口获取企业规模
            JSONObject scaleResult = callTianyanchaApi("/services/open/ic/scale", creditCode);

            if (scaleResult != null && scaleResult.getInteger("error_code") == 0) {
                // 根据文档，result字段直接就是企业规模字符串
                String scale = scaleResult.getString("result");

                // 直接设置到企业基本信息中
                basicInfo.setScale(scale);
                log.info("企业规模信息同步成功，creditCode: {}, scale: {}", creditCode, scale);
            } else {
                log.error("获取企业规模信息失败，creditCode: {}, result: {}", creditCode, scaleResult);
            }
        } catch (Exception e) {
            log.error("同步企业规模信息失败，creditCode: {}, error: {}", creditCode, e.getMessage(), e);
            // 企业规模获取失败不影响基本信息保存，只记录日志
        }
    }


    /**
     * 同步企业股东信息
     * @param creditCode 统一社会信用代码
     */
    public void syncEnterpriseShareholder(String creditCode) {
        try {
            List<EnterpriseShareholder> allShareholderList = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 20; // 天眼查API最大支持20条

            while (true) {
                // 调用821接口获取企业股东信息（分页）
                JSONObject result = callTianyanchaApi("/services/open/ic/holder/2.0", creditCode, pageNum, pageSize);

                if (result != null && result.getInteger("error_code") == 0) {
                    JSONObject resultData = result.getJSONObject("result");
                    JSONArray holderList = resultData.getJSONArray("items");

                    if (holderList == null || holderList.isEmpty()) {
                        // 没有更多数据，退出循环
                        break;
                    }
                    for (int i = 0; i < holderList.size(); i++) {
                        JSONObject holder = holderList.getJSONObject(i);

                        EnterpriseShareholder shareholder = new EnterpriseShareholder();
                        shareholder.setCgid(holder.getLong("cgid"));

                        // 处理认缴信息
                        JSONArray capitalArray = holder.getJSONArray("capital");
                        if (capitalArray != null) {
                            List<EnterpriseShareholder.CapitalInfo> capitalList = new ArrayList<>();
                            for (int j = 0; j < capitalArray.size(); j++) {
                                JSONObject capitalObj = capitalArray.getJSONObject(j);
                                EnterpriseShareholder.CapitalInfo capitalInfo = new EnterpriseShareholder.CapitalInfo();
                                capitalInfo.setAmomon(capitalObj.getString("amomon"));
                                capitalInfo.setPayment(capitalObj.getString("payment"));
                                capitalInfo.setTime(capitalObj.getString("time"));
                                capitalInfo.setPercent(capitalObj.getString("percent"));
                                capitalList.add(capitalInfo);
                            }
                            shareholder.setCapital(capitalList);
                        }

                        shareholder.setFtShareholding(holder.getLong("ftShareholding"));
                        shareholder.setName(holder.getString("name"));

                        // 处理实缴信息
                        JSONArray capitalActlArray = holder.getJSONArray("capitalActl");
                        if (capitalActlArray != null) {
                            List<EnterpriseShareholder.CapitalInfo> capitalActlList = new ArrayList<>();
                            for (int j = 0; j < capitalActlArray.size(); j++) {
                                JSONObject capitalActlObj = capitalActlArray.getJSONObject(j);
                                EnterpriseShareholder.CapitalInfo capitalActlInfo = new EnterpriseShareholder.CapitalInfo();
                                capitalActlInfo.setAmomon(capitalActlObj.getString("amomon"));
                                capitalActlInfo.setPayment(capitalActlObj.getString("payment"));
                                capitalActlInfo.setTime(capitalActlObj.getString("time"));
                                capitalActlInfo.setPercent(capitalActlObj.getString("percent"));
                                capitalActlList.add(capitalActlInfo);
                            }
                            shareholder.setCapitalActl(capitalActlList);
                        }

                        shareholder.setLogo(holder.getString("logo"));
                        shareholder.setAlias(holder.getString("alias"));
                        shareholder.setShareholderId(holder.getLong("id"));
                        shareholder.setType(holder.getInteger("type"));
                        shareholder.setHcgid(holder.getString("hcgid"));

                        allShareholderList.add(shareholder);
                    }

                    // 如果返回的数据少于pageSize，说明已经是最后一页
                    if (holderList.size() < pageSize) {
                        break;
                    }

                    pageNum++; // 获取下一页
                } else {
                    log.warn("获取企业股东信息失败，creditCode: {}, pageNum: {}, result: {}", creditCode, pageNum, result);
                    break;
                }
            }

            enterpriseShareholderService.batchSave(creditCode, allShareholderList);
            log.info("企业股东信息同步成功，creditCode: {}, totalCount: {}", creditCode, allShareholderList.size());
        } catch (Exception e) {
            log.error("同步企业股东信息失败，creditCode: {}, error: {}", creditCode, e.getMessage(), e);
            throw new BusinessException("同步企业股东信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 同步最终受益人信息
     * @param creditCode 统一社会信用代码
     */
    public void syncEnterpriseBeneficiary(String creditCode) {
        try {
            List<EnterpriseBeneficiary> allBeneficiaries = new ArrayList<>();
            int pageNum = 1;
            int pageSize = 20; // 天眼查API最大支持20条

            while (true) {
                // 调用945接口获取最终受益人信息（分页）
                JSONObject result = callTianyanchaApi("/services/open/ic/humanholding/2.0", creditCode, pageNum, pageSize);

                if (result != null && result.getInteger("error_code") == 0) {
                    JSONObject resultData = result.getJSONObject("result");
                    JSONArray beneficiaryList = resultData.getJSONArray("items");

                    if (beneficiaryList == null || beneficiaryList.size() == 0) {
                        // 没有更多数据，退出循环
                        break;
                    }

                    for (int i = 0; i < beneficiaryList.size(); i++) {
                        JSONObject beneficiary = beneficiaryList.getJSONObject(i);

                        EnterpriseBeneficiary enterpriseBeneficiary = new EnterpriseBeneficiary();
                        enterpriseBeneficiary.setToco(beneficiary.getInteger("toco"));
                        enterpriseBeneficiary.setTotal(beneficiary.getInteger("total"));

                        // 处理法人链信息（两层数组结构）
                        JSONArray chainListArray = beneficiary.getJSONArray("chainList");
                        if (chainListArray != null) {
                            List<List<EnterpriseBeneficiary.ChainInfo>> chainList = new ArrayList<>();
                            for (int j = 0; j < chainListArray.size(); j++) {
                                JSONArray chainGroupArray = chainListArray.getJSONArray(j);
                                if (chainGroupArray != null) {
                                    List<EnterpriseBeneficiary.ChainInfo> chainGroup = new ArrayList<>();
                                    for (int k = 0; k < chainGroupArray.size(); k++) {
                                        JSONObject chainObj = chainGroupArray.getJSONObject(k);
                                        EnterpriseBeneficiary.ChainInfo chainInfo = new EnterpriseBeneficiary.ChainInfo();
                                        chainInfo.setInvestType(chainObj.getInteger("investType"));
                                        chainInfo.setId(chainObj.getLong("id"));
                                        chainInfo.setType(chainObj.getString("type"));
                                        chainInfo.setTitle(chainObj.getString("title"));
                                        chainInfo.setValue(chainObj.getString("value"));
                                        chainInfo.setCid(chainObj.getLong("cid"));
                                        chainInfo.setInfo(chainObj.getString("info"));
                                        chainGroup.add(chainInfo);
                                    }
                                    chainList.add(chainGroup);
                                }
                            }
                            enterpriseBeneficiary.setChainList(chainList);
                        }

                        enterpriseBeneficiary.setName(beneficiary.getString("name"));
                        enterpriseBeneficiary.setLogo(beneficiary.getString("logo"));
                        enterpriseBeneficiary.setBeneficiaryId(beneficiary.getLong("id"));
                        enterpriseBeneficiary.setType(beneficiary.getString("type"));
                        enterpriseBeneficiary.setPercent(beneficiary.getString("percent"));
                        enterpriseBeneficiary.setHcgid(beneficiary.getString("hcgid"));
                        enterpriseBeneficiary.setCid(beneficiary.getLong("cid"));

                        allBeneficiaries.add(enterpriseBeneficiary);
                    }

                    // 如果返回的数据少于pageSize，说明已经是最后一页
                    if (beneficiaryList.size() < pageSize) {
                        break;
                    }

                    pageNum++; // 获取下一页
                } else {
                    log.warn("获取最终受益人信息失败，creditCode: {}, pageNum: {}, result: {}", creditCode, pageNum, result);
                    break;
                }
            }

            enterpriseBeneficiaryService.batchSave(creditCode, allBeneficiaries);
            log.info("最终受益人信息同步成功，creditCode: {}, totalCount: {}", creditCode, allBeneficiaries.size());
        } catch (Exception e) {
            log.error("同步最终受益人信息失败，creditCode: {}, error: {}", creditCode, e.getMessage(), e);
            throw new BusinessException("同步最终受益人信息失败：" + e.getMessage());
        }
    }

    /**
     * 同步财务信息
     * @param creditCode 统一社会信用代码
     */
    public void syncEnterpriseFinancial(String creditCode) {
        try {
            // 调用798接口获取上市公司财务简析
            JSONObject result = callTianyanchaApi("/services/v4/open/financialAnalysis", creditCode);

            if (result != null && result.getInteger("error_code") == 0) {
                JSONObject resultData = result.getJSONObject("result");

                EnterpriseFinancial enterpriseFinancial = new EnterpriseFinancial();
                enterpriseFinancial.setCreditCode(creditCode);
                enterpriseFinancial.setTotal(resultData.getInteger("total"));

                // 处理净资产信息
                JSONObject netAssetsObj = resultData.getJSONObject("netAssets");
                if (netAssetsObj != null) {
                    enterpriseFinancial.setNetAssets(parseFinancialData(netAssetsObj));
                }

                // 处理净利率信息
                JSONObject netInterestRateObj = resultData.getJSONObject("netInterestRate");
                if (netInterestRateObj != null) {
                    enterpriseFinancial.setNetInterestRate(parseFinancialData(netInterestRateObj));
                }

                // 处理总资产信息
                JSONObject totalAssetsObj = resultData.getJSONObject("totalAssets");
                if (totalAssetsObj != null) {
                    enterpriseFinancial.setTotalAssets(parseFinancialData(totalAssetsObj));
                }

                // 处理营业收入信息
                JSONObject businessIncomeObj = resultData.getJSONObject("businessIncome");
                if (businessIncomeObj != null) {
                    enterpriseFinancial.setBusinessIncome(parseFinancialData(businessIncomeObj));
                }

                // 处理净利润信息
                JSONObject netProfitObj = resultData.getJSONObject("netProfit");
                if (netProfitObj != null) {
                    enterpriseFinancial.setNetProfit(parseFinancialData(netProfitObj));
                }

                // 处理毛利率信息
                JSONObject grossInterestRateObj = resultData.getJSONObject("grossInterestRate");
                if (grossInterestRateObj != null) {
                    enterpriseFinancial.setGrossInterestRate(parseFinancialData(grossInterestRateObj));
                }

                enterpriseFinancialService.saveOrUpdate(enterpriseFinancial);
                log.info("财务信息同步成功，creditCode: {}", creditCode);
            } else {
                log.warn("获取财务信息失败，creditCode: {}, result: {}", creditCode, result);
            }
        } catch (Exception e) {
            log.error("同步财务信息失败，creditCode: {}, error: {}", creditCode, e.getMessage(), e);
            throw new BusinessException("同步财务信息失败：" + e.getMessage());
        }
    }

    /**
     * 解析财务数据
     * @param financialObj 财务数据JSON对象
     * @return 财务数据对象
     */
    private EnterpriseFinancial.FinancialData parseFinancialData(JSONObject financialObj) {
        EnterpriseFinancial.FinancialData financialData = new EnterpriseFinancial.FinancialData();
        financialData.setInfo(financialObj.getString("info"));
        financialData.setTitle(financialObj.getString("title"));

        JSONArray timeArray = financialObj.getJSONArray("time");
        if (timeArray != null) {
            financialData.setTime(timeArray.toJavaList(String.class));
        }

        JSONArray listArray = financialObj.getJSONArray("list");
        if (listArray != null) {
            List<EnterpriseFinancial.FinancialItem> itemList = new ArrayList<>();
            for (int i = 0; i < listArray.size(); i++) {
                JSONObject itemObj = listArray.getJSONObject(i);
                EnterpriseFinancial.FinancialItem item = new EnterpriseFinancial.FinancialItem();
                item.setAmount(itemObj.getLong("amount"));
                item.setConvertAmount(itemObj.getString("convertAmount"));
                item.setYear(itemObj.getString("year"));
                itemList.add(item);
            }
            financialData.setList(itemList);
        }

        return financialData;
    }

    /**
     * 调用天眼查API
     * @param apiPath API路径
     * @param keyword 查询关键字（企业名称或统一社会信用代码）
     * @return API响应结果
     */
    private JSONObject callTianyanchaApi(String apiPath, String keyword) {
        return callTianyanchaApi(apiPath, keyword, null, null);
    }

    /**
     * 调用天眼查API（支持分页）
     * @param apiPath API路径
     * @param keyword 查询关键字（企业名称或统一社会信用代码）
     * @param pageNum 页码（可为null）
     * @param pageSize 每页条数（可为null）
     * @return API响应结果
     */
    private JSONObject callTianyanchaApi(String apiPath, String keyword, Integer pageNum, Integer pageSize) {
        try {
            StringBuilder urlBuilder = new StringBuilder(apiBaseUrl + apiPath + "?keyword=" + keyword);

            // 添加分页参数
            if (pageNum != null) {
                urlBuilder.append("&pageNum=").append(pageNum);
            }
            if (pageSize != null) {
                urlBuilder.append("&pageSize=").append(pageSize);
            }

            String url = urlBuilder.toString();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", apiToken);

            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                return JSON.parseObject(response.getBody());
            } else {
                log.error("调用天眼查API失败，url: {}, status: {}", url, response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            log.error("调用天眼查API异常，apiPath: {}, keyword: {}, pageNum: {}, pageSize: {}, error: {}",
                    apiPath, keyword, pageNum, pageSize, e.getMessage(), e);
            return null;
        }
    }
}
