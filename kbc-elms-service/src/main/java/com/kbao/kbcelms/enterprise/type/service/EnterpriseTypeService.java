package com.kbao.kbcelms.enterprise.type.service;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.kbcbsc.model.Pagination;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcbsc.service.nosql.BaseMongoServiceImpl;
import com.kbao.tool.util.SysLoginUtils;
import com.kbao.kbcelms.enterprise.type.dao.EnterpriseTypeDao;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeEnumVO;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeVO;
import com.kbao.kbcelms.enterprise.util.EnterpriseTypeRuleUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.data.mongodb.core.query.Criteria;import org.springframework.data.mongodb.core.query.Query;import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业类型业务逻辑层
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class EnterpriseTypeService extends BaseMongoServiceImpl<EnterpriseType, String, EnterpriseTypeDao> {
    
    /**
     * 分页查询企业类型列表
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseType> page(PageRequest<EnterpriseType> page) {
        EnterpriseType queryParam = page.getParam();
        Criteria criteria = new Criteria();
        if (StringUtils.hasText(queryParam.getName())) {
            criteria.and("name").regex(queryParam.getName(), "i"); // 忽略大小写的模糊查询
        }
        if (StringUtils.hasText(queryParam.getCode())) {
            criteria.and("code").is(queryParam.getCode());
        }
        Pagination<EnterpriseType> pagination = new Pagination<>(page.getPageNum(), page.getPageSize(), "priority");
        PageInfo<EnterpriseType> pageInfo = super.page(new Query(criteria), pagination);
        // 为每个企业类型生成展示文本
        for (EnterpriseType enterpriseType : pageInfo.getList()) {
            enrichEnterpriseTypeDisplayText(enterpriseType);
        }
        return pageInfo;
    }

    /**
     * 分页查询企业类型列表（包含格式化的规则显示）
     * @param page 分页查询参数
     * @return 分页结果
     */
    public PageInfo<EnterpriseTypeVO> pageWithRuleDisplay(PageRequest<EnterpriseType> page) {
        // 查询数据
        PageInfo<EnterpriseType> pageInfo = this.page(page);

        // 转换为VO并组装规则显示
        List<EnterpriseTypeVO> voList = pageInfo.getList().stream()
                .map(this::convertToVOWithRuleDisplay)
                .collect(Collectors.toList());

        // 构建返回结果
        PageInfo<EnterpriseTypeVO> result = new PageInfo<>();
        result.setList(voList);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());

        return result;
    }

    /**
     * 创建企业类型
     * @param enterpriseType 企业类型信息
     * @return 创建的企业类型
     */
    public EnterpriseType add(EnterpriseType enterpriseType) {
        // 校验编码唯一性
        validateCodeUnique(enterpriseType.getCode(), null);
        // 设置创建信息
        Date now = new Date();
        String currentUserId = SysLoginUtils.getUser().getUserId();
        String tenantId = SysLoginUtils.getUser().getTenantId();
        enterpriseType.setCreateTime(now);
        enterpriseType.setUpdateTime(now);
        enterpriseType.setCreateId(currentUserId);
        enterpriseType.setUpdateId(currentUserId);
        enterpriseType.setTenantId(tenantId);
        return dao.save(enterpriseType);
    }


    /**
     * 校验编码唯一性
     * @param code 编码
     * @param excludeId 排除的ID
     */
    private void validateCodeUnique(String code, String excludeId) {
        EnterpriseType existingType = dao.findByCodeExcludeId(code, excludeId);
        if (existingType != null) {
            throw new BusinessException("企业类型编码已存在");
        }
    }


    /**
     * 查询企业类型枚举列表
     * @return 企业类型枚举列表，按priority排序
     */
    public List<EnterpriseTypeEnumVO> getEnterpriseTypeEnum() {
        String tenantId = SysLoginUtils.getUser().getTenantId();
        Criteria criteria = Criteria.where("tenantId").is(tenantId);
        Query query = new Query(criteria).with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.ASC, "priority"));

        List<EnterpriseType> enterpriseTypes = dao.find(query);

        return enterpriseTypes.stream()
                .map(type -> new EnterpriseTypeEnumVO(type.getName(), type.getCode()))
                .collect(Collectors.toList());
    }

    /**
     * 转换为VO并组装规则显示文本
     * @param entity 实体对象
     * @return VO对象
     */
    private EnterpriseTypeVO convertToVOWithRuleDisplay(EnterpriseType entity) {
        EnterpriseTypeVO vo = new EnterpriseTypeVO();
        BeanUtils.copyProperties(entity, vo);

        // 确保规则完整性
        ensureDefaultRules(vo);

        return vo;
    }

    /**
     * 确保默认规则存在
     * @param vo VO对象
     */
    private void ensureDefaultRules(EnterpriseTypeVO vo) {
        if (vo.getRules() == null) {
            vo.setRules(new ArrayList<>());
        }

        // 确保从业人员规则存在
        if (vo.getRules().stream().noneMatch(r -> "employeeCount".equals(r.getField()))) {
            EnterpriseTypeRule employeeRule = new EnterpriseTypeRule();
            employeeRule.setField("employeeCount");
            employeeRule.setFieldName("从业人员");
            employeeRule.setUnit("人");
            vo.getRules().add(employeeRule);
        }

        // 确保营业收入规则存在
        if (vo.getRules().stream().noneMatch(r -> "revenue".equals(r.getField()))) {
            EnterpriseTypeRule revenueRule = new EnterpriseTypeRule();
            revenueRule.setField("revenue");
            revenueRule.setFieldName("营业收入");
            revenueRule.setUnit("万元");
            vo.getRules().add(revenueRule);
        }
    }

    /**
     * 为企业类型生成展示文本
     * @param enterpriseType 企业类型
     */
    private void enrichEnterpriseTypeDisplayText(EnterpriseType enterpriseType) {
        if (enterpriseType != null && !CollectionUtils.isEmpty(enterpriseType.getRules())) {
            enterpriseType.setEmployeeRangeText(EnterpriseTypeRuleUtil.generateEmployeeRangeText(enterpriseType.getRules()));
            enterpriseType.setRevenueRangeText(EnterpriseTypeRuleUtil.generateRevenueRangeText(enterpriseType.getRules()));
        }
    }
}
