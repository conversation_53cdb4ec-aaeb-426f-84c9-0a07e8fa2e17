package com.kbao.kbcelms.enterprise.util;

import com.kbao.kbcelms.enterprise.type.model.EnterpriseTypeRule;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业类型规则工具类
 * <AUTHOR>
 * @date 2025-07-28
 */
public class EnterpriseTypeRuleUtil {
    
    /**
     * 生成员工规模范围展示文本
     * @param rules 规则列表
     * @return 员工规模范围文本
     */
    public static String generateEmployeeRangeText(List<EnterpriseTypeRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return "-";
        }
        List<EnterpriseTypeRule> employeeRules = rules.stream()
            .filter(rule -> "employeeCount".equals(rule.getField()))
            .collect(Collectors.toList());
        return generateFieldText(employeeRules, "人");
    }

    /**
     * 生成营收规模范围展示文本
     * @param rules 规则列表
     * @return 营收规模范围文本
     */
    public static String generateRevenueRangeText(List<EnterpriseTypeRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return "-";
        }
        List<EnterpriseTypeRule> revenueRules = rules.stream()
            .filter(rule -> "revenue".equals(rule.getField()))
            .collect(Collectors.toList());
        return generateFieldText(revenueRules, "万元");
    }
    
    /**
     * 生成字段展示文本（将同一字段的条件用分号分割）
     * @param rules 规则列表
     * @param unit 单位
     * @return 展示文本
     */
    private static String generateFieldText(List<EnterpriseTypeRule> rules, String unit) {
        if (CollectionUtils.isEmpty(rules)) {
            return "-";
        }
        return rules.stream()
            .map(rule -> generateRuleText(rule, unit))
            .collect(Collectors.joining(";"));
    }

    /**
     * 生成单个规则的文本
     */
    private static String generateRuleText(EnterpriseTypeRule rule, String unit) {
        if (rule.getMinValue() == null && rule.getMaxValue() == null) {
            return "无限制";
        }

        StringBuilder text = new StringBuilder();

        if (rule.getMinValue() != null && rule.getMaxValue() != null) {
            // 双边界：>= 100万元 < 300万元
            text.append(">= ").append(formatValue(rule.getMinValue(), unit))
                .append(" < ").append(formatValue(rule.getMaxValue(), unit));
        } else if (rule.getMinValue() != null) {
            // 只有下界：>= 100万元
            text.append(">= ").append(formatValue(rule.getMinValue(), unit));
        } else {
            // 只有上界：< 300万元
            text.append("< ").append(formatValue(rule.getMaxValue(), unit));
        }

        return text.toString();
    }
    
    /**
     * 格式化数值显示
     * @param value 数值
     * @param unit 单位
     * @return 格式化后的文本
     */
    private static String formatValue(Double value, String unit) {
        if (value == null) {
            return "";
        }

        if ("亿元".equals(unit)) {
            return value + "亿元";
        } else if ("万元".equals(unit)) {
            return value + "万元";
        } else {
            // 员工数量等其他单位
            return value.intValue() + unit;
        }
    }

}
