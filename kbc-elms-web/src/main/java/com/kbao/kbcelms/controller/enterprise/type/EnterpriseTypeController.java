package com.kbao.kbcelms.controller.enterprise.type;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.BaseController;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcbsc.model.RequestObjectPage;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeVO;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeEnumVO;
import com.kbao.kbcelms.enterprise.type.model.EnterpriseType;
import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;
import com.kbao.kbcelms.enterprise.util.EnterpriseTypeRuleUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 企业类型管理控制器
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/api/enterprise/type")
@Api(tags = "企业类型管理")
public class EnterpriseTypeController extends BaseController {
    
    @Autowired
    private EnterpriseTypeService enterpriseTypeService;
    
    /**
     * 分页查询企业类型列表
     */
    @ApiOperation(value = "分页查询企业类型列表", notes = "支持按名称、编码模糊查询")
    @PostMapping("/page")
    @LogAnnotation(module = "企业类型管理", recordRequestParam = true, action = "查询", desc = "分页查询企业类型列表")
    public Result<PageInfo<EnterpriseTypeVO>> page(@RequestBody PageRequest<EnterpriseType> page) {
        PageInfo<EnterpriseTypeVO> pageInfo = enterpriseTypeService.pageWithRuleDisplay(page);
        return Result.succeed(pageInfo, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 根据ID查询企业类型详情
     */
    @ApiOperation(value = "根据ID查询企业类型详情", notes = "根据ID查询企业类型详情")
    @PostMapping("/detail")
    @LogAnnotation(module = "企业类型管理", recordRequestParam = true, action = "查询", desc = "查询企业类型详情")
    public Result<EnterpriseType> detail(@RequestBody EnterpriseType enterpriseType) {
        EnterpriseType data = enterpriseTypeService.findById(enterpriseType.getId());
        return Result.succeed(data, ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 创建企业类型
     */
    @ApiOperation(value = "创建企业类型", notes = "创建新的企业类型")
    @PostMapping("/add")
    @LogAnnotation(module = "企业类型管理", recordRequestParam = true, action = "创建", desc = "创建企业类型")
    public Result add(@RequestBody @Validated EnterpriseType enterpriseType, BindingResult bindingResult) {
        checkValidator("创建企业类型", bindingResult, enterpriseType);

        enterpriseTypeService.add(enterpriseType);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 更新企业类型
     */
    @ApiOperation(value = "更新企业类型", notes = "更新企业类型信息")
    @PostMapping("/update")
    @LogAnnotation(module = "企业类型管理", recordRequestParam = true, action = "更新", desc = "更新企业类型")
    public Result update(@RequestBody @Validated EnterpriseType enterpriseType, BindingResult bindingResult) {
        checkValidator("更新企业类型", bindingResult, enterpriseType);

        enterpriseTypeService.update(enterpriseType);
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }
    
    /**
     * 删除企业类型
     */
    @ApiOperation(value = "删除企业类型", notes = "根据ID删除企业类型")
    @PostMapping("/delete")
    @LogAnnotation(module = "企业类型管理", recordRequestParam = true, action = "删除", desc = "删除企业类型")
    public Result delete(@RequestBody EnterpriseType enterpriseType) {
        enterpriseTypeService.remove(enterpriseType.getId());
        return Result.succeed(ResultStatusEnum.SUCCESS.getMsg());
    }

    /**
     * 查询企业类型枚举列表
     */
    @ApiOperation(value = "查询企业类型枚举列表", notes = "获取所有企业类型的名称和编码，按优先级排序")
    @GetMapping("/enum")
    @LogAnnotation(module = "企业类型管理", recordRequestParam = false, action = "查询", desc = "查询企业类型枚举列表")
    public Result<List<EnterpriseTypeEnumVO>> getEnterpriseTypeEnum() {
        List<EnterpriseTypeEnumVO> enumList = enterpriseTypeService.getEnterpriseTypeEnum();
        return Result.succeed(enumList, ResultStatusEnum.SUCCESS.getMsg());
    }
}
