<template>
  <EditPageContainer
      :title="pageTitle"
      :icon="pageIcon"
      :breadcrumb-items="breadcrumbItems"
    :is-view="isView"
    :loading="loading"
      @back="handleBack"
    @save="handleSave"
      @breadcrumb-click="handleBreadcrumbClick"
    >
    <UniversalForm
      ref="universalForm"
      :formData="form"
      :formRules="formRules"
      :formGroups="formGroups"
      :labelWidth="'100px'"
      :rowGutter="20"
      :isView="isView"
      hide-required-asterisk
    />
  </EditPageContainer>
</template>

<script>
import EditPageContainer from '@/components/layouts/EditPageContainer.vue'
import UniversalForm from '@/components/layouts/UniversalForm.vue'
import { getEnterpriseTypeDetail, createEnterpriseType, updateEnterpriseType } from '@/api/enterprise/type.js'

export default {
  name: 'EnterpriseTypeEdit',
  components: { EditPageContainer, UniversalForm },
  data() {
    return {
      isEdit: false,
      isView: false,
      loading: false,
      rulesLoading: false,
      form: {
        name: '',
        code: '',
        description: '',
        priority: 1,
        rules: []
      },
      formGroups: [
        {
          title: '基本信息',
          icon: 'el-icon-info',
          fields: [
            [
              { prop: 'name', label: '类型名称', type: 'input', placeholder: '请输入企业类型名称', maxlength: 50, showWordLimit: true },
              { prop: 'code', label: '类型编码', type: 'input', placeholder: '请输入类型编码，如：SMALL', maxlength: 20 },
              { prop: 'priority', label: '优先级', type: 'number', placeholder: '请设置优先级', min: 1, max: 10, step: 1 }
            ],
            [
              { prop: 'description', label: '类型描述', type: 'textarea', placeholder: '请输入类型描述', maxlength: 200, rows: 3, showWordLimit: true, span: 24 }
            ]
          ]
        },
        {
          title: '判定规则',
          icon: 'el-icon-s-operation',
          fields: [
            [
              {
                type: 'custom',
                prop: 'rules',
                label: '判定规则',
                span: 24,
                render: (h, { formData, isView }) => {
                  return h('div', { class: 'rules-section' }, [
                    // 从业人员规则
                    h('div', { class: 'rule-item' }, [
                      h('span', { class: 'rule-label' }, '从业人员：'),
                      h('el-input-number', {
                        props: {
                          value: formData.rules && formData.rules[0] ? formData.rules[0].minValue : null,
                          placeholder: '最小值',
                          precision: 0,
                          disabled: isView
                        },
                        on: {
                          input: (val) => {
                            this.updateRuleValue(0, 'minValue', val)
                          }
                        }
                      }),
                      h('span', { class: 'separator' }, '≤ 人数 <'),
                      h('el-input-number', {
                        props: {
                          value: formData.rules && formData.rules[0] ? formData.rules[0].maxValue : null,
                          placeholder: '最大值',
                          precision: 0,
                          disabled: isView
                        },
                        on: {
                          input: (val) => {
                            this.updateRuleValue(0, 'maxValue', val)
                          }
                        }
                      }),
                      h('span', { class: 'unit' }, '人')
                    ]),
                    // 营业收入规则
                    h('div', { class: 'rule-item' }, [
                      h('span', { class: 'rule-label' }, '营业收入：'),
                      h('el-input-number', {
                        props: {
                          value: formData.rules && formData.rules[1] ? formData.rules[1].minValue : null,
                          placeholder: '最小值',
                          precision: 2,
                          disabled: isView
                        },
                        on: {
                          input: (val) => {
                            this.updateRuleValue(1, 'minValue', val)
                          }
                        }
                      }),
                      h('span', { class: 'separator' }, '≤ 收入 <'),
                      h('el-input-number', {
                        props: {
                          value: formData.rules && formData.rules[1] ? formData.rules[1].maxValue : null,
                          placeholder: '最大值',
                          precision: 2,
                          disabled: isView
                        },
                        on: {
                          input: (val) => {
                            this.updateRuleValue(1, 'maxValue', val)
                          }
                        }
                      }),
                      h('el-select', {
                        props: {
                          value: formData.rules && formData.rules[1] ? formData.rules[1].unit : '万元',
                          disabled: isView
                        },
                        style: { width: '80px' },
                        on: {
                          input: (val) => {
                            this.updateRuleValue(1, 'unit', val)
                          }
                        }
                      }, [
                        h('el-option', { props: { label: '万元', value: '万元' } }),
                        h('el-option', { props: { label: '亿元', value: '亿元' } })
                      ])
                    ]),
                    // 规则预览
                    h('div', { class: 'rule-preview' }, [
                      h('h5', '规则预览：'),
                      h('div', { class: 'preview-content' },
                        this.getRulePreview(formData.rules)
                      )
                    ])
                  ])
                }
              }
            ]
          ]
        }
      ],
      formRules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }],
        code: [{ required: true, message: '请输入类型编码', trigger: 'blur' }],
        priority: [{ required: true, message: '请设置优先级', trigger: 'blur' }]
      }
    }
  },
  computed: {
    pageTitle() {
      if (this.isView) return '查看企业类型'
      return this.isEdit ? '编辑企业类型' : '新建企业类型'
    },
    pageIcon() {
      return 'el-icon-s-data'
    },
    breadcrumbItems() {
      return [
        { text: '企业类型管理', to: { name: 'enterpriseType' }, icon: 'el-icon-s-data' },
        { text: this.pageTitle, icon: this.pageIcon }
      ]
    }
  },
  created() {
    const id = this.$route.params.id
    const mode = this.$route.query.mode
    if (mode === 'view') this.isView = true
    if (id) {
      this.isEdit = true
      this.loadData(id)
    } else {
      // 新建时确保默认规则
      this.ensureDefaultRules()
    }
  },
  methods: {
    async loadData(id) {
      try {
        const response = await getEnterpriseTypeDetail(id)
        // 后端直接返回数据，不需要判断code
        this.form = { ...response }
        // 确保规则结构正确
        this.ensureDefaultRules()
      } catch (error) {
        this.$message.error('加载数据失败')
        console.error('加载数据失败:', error)
      }
    },

    // 确保默认规则存在
    ensureDefaultRules() {
      if (!this.form.rules) {
        this.form.rules = []
      }

      // 确保从业人员规则存在
      if (!this.form.rules.find(r => r.field === 'employeeCount')) {
        this.form.rules.push({
          field: 'employeeCount',
          fieldName: '从业人员',
          unit: '人',
          minValue: null,
          maxValue: null
        })
      }

      // 确保营业收入规则存在
      if (!this.form.rules.find(r => r.field === 'revenue')) {
        this.form.rules.push({
          field: 'revenue',
          fieldName: '营业收入',
          unit: '万元',
          minValue: null,
          maxValue: null
        })
      }

      // 确保规则顺序：从业人员在前，营业收入在后
      this.form.rules.sort((a, b) => {
        const order = { employeeCount: 0, revenue: 1 }
        return order[a.field] - order[b.field]
      })
    },

    // 更新规则值
    updateRuleValue(index, field, value) {
      if (!this.form.rules[index]) {
        this.ensureDefaultRules()
      }
      this.$set(this.form.rules[index], field, value)
    },

    // 获取规则预览
    getRulePreview(rules) {
      if (!rules || rules.length === 0) {
        return '从业人员：无限制；营业收入：无限制'
      }

      const previews = rules.map(rule => {
        if (!rule.minValue && !rule.maxValue) {
          return `${rule.fieldName}：无限制`
        }

        let display = rule.fieldName + '：'

        if (rule.minValue && rule.maxValue) {
          display += `>= ${this.formatValue(rule.minValue, rule.unit)} < ${this.formatValue(rule.maxValue, rule.unit)}`
        } else if (rule.minValue) {
          display += `>= ${this.formatValue(rule.minValue, rule.unit)}`
        } else if (rule.maxValue) {
          display += `< ${this.formatValue(rule.maxValue, rule.unit)}`
        }

        return display
      })

      return previews.join('；')
    },

    // 格式化数值显示
    formatValue(value, unit) {
      if (!value) return ''

      if (unit === '亿元') {
        return `${value}亿元`
      } else if (unit === '万元') {
        return `${value}万元`
      } else {
        return `${value}${unit}`
      }
    },
    async handleSave() {
      await this.$refs.universalForm.validate()
      // 确保规则结构正确
      this.ensureDefaultRules()

      this.loading = true
      try {
        if (this.isEdit) {
          // 更新时需要传入id
              const updateData = { ...this.form, id: this.$route.params.id }
              await updateEnterpriseType(updateData)
            } else {
              await createEnterpriseType(this.form)
            }
            this.$message.success(this.isEdit ? '更新成功' : '创建成功')
            this.$router.back()
          } catch (error) {
            this.$message.error('保存失败')
            console.error('保存失败:', error)
          } finally {
            this.loading = false
          }
    },
    handleBack() {
      this.$router.back()
    },
    handleBreadcrumbClick(item) {
      if (item.to && item.to.name) {
        this.$router.push({ name: item.to.name })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.rules-section {
  .rule-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 8px;

    .rule-label {
      width: 80px;
      font-weight: 500;
      color: #303133;
    }

    .separator {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
      margin: 0 4px;
    }

    .unit {
      color: #909399;
      font-size: 12px;
      margin-left: 4px;
    }
  }

  .rule-preview {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;

    h5 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }

    .preview-content {
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}
</style>
