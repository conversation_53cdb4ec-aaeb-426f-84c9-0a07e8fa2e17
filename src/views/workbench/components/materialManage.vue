<template>
  <div class="member-manage">
    <!-- 生态服务 -->
    <TableToolTemp :toolListProps="serviceMenuListPros" class="log-tool" ></TableToolTemp>
    <el-form
      ref="detailForm"
      :model="detailForm"
      label-width="150px"
      label-position="left"
    >
      <el-form-item label="是否附加生态服务" prop="divisionId">
        <el-radio-group v-model="detailForm.hasService">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-table :data="serviceProductList" class="dt-table" style="width: 100%" v-hover row-key="id">
      <el-table-column align="center" prop="companyCode" label="服务类型"  width="250">
        <template slot-scope="scope">
          <span class="communication-item">{{ scope.row.serviceType }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="policyNo" label="服务产品" width="250" >
        <template slot-scope="scope">
          <div >
            {{ scope.row.productName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200" >
        <template slot-scope="scope">
          <div class="action-buttons" v-if="!(scope.row.isDefault==1 && scope.row.joinType==1)">
            <el-button class="btn-center" type="text" @click="opendProductPopup()">
              选择产品
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 投标情况 -->
    <TableToolTemp :toolListProps="bideMenuListPros" class="log-tool" ></TableToolTemp>
    <el-form
      ref="detailForm"
      :model="detailForm"
      label-width="150px"
      label-position="left"
    >
      <el-form-item label="是否投标" prop="divisionId">
        <el-radio-group v-model="detailForm.isBid">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <!-- 经纪委托授权书 -->
    <TableToolTemp :toolListProps="authMenuListPros" class="log-tool" ></TableToolTemp>

    <!-- 询价记录 -->
    <TableToolTemp :toolListProps="inquiryMenuListPros" class="log-tool" ></TableToolTemp>

    <!-- 排分结果 -->
    <TableToolTemp :toolListProps="resultMenuListPros" class="log-tool" ></TableToolTemp>

    <!-- 选择产品弹窗 -->
    <DtPopup :isShow.sync="showProductPopup" @close="showProductPopup=false"  title="选择生态服务产品" small :footer="true" width="800" >
      <!-- 选择产品 -->
      <div>
        <el-table :data="healthProductList" class="dt-table" style="width: 100%" v-hover row-key="id" @row-click="getProductInfo">
          <el-table-column width="55" fixed="left">
            <template slot-scope="scope">
              <el-radio
                class="radio"
                :label="scope.row"
                v-model="selHealthProduct"
              >{{""}}</el-radio>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="健康服务"  width="250">
            <template slot-scope="scope">
              <span class="communication-item">{{ scope.row.productName }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table :data="rescueProductList" class="dt-table" style="width: 100%" v-hover row-key="id">
          <el-table-column width="55" fixed="left">
            <template slot-scope="scope">
              <el-radio
                class="radio"
                :label="scope.row"
                v-model="selRescueProduct"
              >{{""}}</el-radio>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="productName" label="救援服务"  width="250">
            <template slot-scope="scope">
              <span class="communication-item">{{ scope.row.productName }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 产品详情渲染 -->
      <div>

      </div>
    </DtPopup>
  </div>
</template>

<script>
import TableToolTemp from "@/components/layouts/TableToolTemp";
import SearchForm from "@/components/layouts/SearchForm";
import DtPopup from "@/components/layouts/DtPopup";
import {esptProductInfo, esptProductList, updateOrder} from "../../../api/workbench";



export default {
  name: "materialManage",
  inject: ['opportunityDetailFn', 'opportunityFn', 'enterpriseFn', 'opportunityProcessLogFn'],
  data() {
    return {
      opportunityId: this.$route.query.id,

      detailForm: {
        hasService:1,
        isBid:1,
      },

      /*** 生态服务 ***/
      serviceMenuListPros: {
        toolTitle: "生态服务"
      },
      // 服务产品列表
      serviceProductList:[
        {serviceType:"健康服务",productName:"健康服务优享版"},
        {serviceType:"救援服务",productName:"境内救援服务"}
      ],
      showProductPopup:false,
      // 健康服务产品列表
      healthProductList:[],
      // 救援服务产品列表
      rescueProductList:[],
      selHealthProduct:{},
      selRescueProduct:{},

      /*** 投标情况 ***/
      bideMenuListPros: {
        toolTitle: "投标情况"
      },

      /*** 经纪委托授权书 ***/
      authMenuListPros: {
        toolTitle: "经纪委托授权书"
      },

      /*** 询价记录 ***/
      inquiryMenuListPros: {
        toolTitle: "询价记录"
      },

      /*** 排分结果 ***/
      resultMenuListPros: {
        toolTitle: "排分结果"
      },

    }
  },
  components: {
    TableToolTemp,
    SearchForm,
    DtPopup
  },
  filters: {

  },
  computed: {
    authSet() {
      return this.$store.getters["layoutStore/getAuthSet"];
    },
    themeObj() {
      return this.$store.getters["layoutStore/getThemeObj"];
    },
    // 从父组件获取注入的数据
    injectedOpportunityDetail() {
      return this.opportunityDetailFn ? this.opportunityDetailFn() : null;
    },
    injectedOpportunity() {
      return this.opportunityFn ? this.opportunityFn() : null;
    },
    injectedEnterprise() {
      return this.enterpriseFn ? this.enterpriseFn() : null;
    },
    injectedOpportunityProcessLog() {
      return this.opportunityProcessLogFn ? this.opportunityProcessLogFn() : null;
    }
  },
  async created() {
    this.initData();
  },
  methods: {

    async initData() {
      this.getEsptProductList();
    },

    /*** 生态服务 ***/
    getProductInfo(row, column, event){
      console.log(row);
    },
    async getEsptProductInfo(row){
      const res = await esptProductInfo();
    },
    opendProductPopup() {
      this.showProductPopup = true;
    },
    async getEsptProductList(){
      const res = await esptProductList();
      if(res){
        if(res.healthProductList){
          this.healthProductList = res.healthProductList;
        }
        if(res.rescueProductList){
          this.rescueProductList = res.rescueProductList;
        }
      }

    },


  }
};
</script>

<style lang="less" scoped>

.location-box {
  width: 360px;
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  cursor: pointer;
  margin: 5px 5px 5px 5px;

  .left {
    cursor: pointer;

    .location {
      margin: 5px 5px 5px 5px;
    }
  }

  .right {
    cursor: pointer;
  }
}
.member-manage {
  .text-content{
    width: 100%;
    text-align: left;
  }
  .text-content div{
    margin: 5px 5px 5px 5px;
  }

  .communication-item {
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;

      i {
        margin-right: 2px;
      }
    }
  }

  .log-form {
    padding-bottom: 20px;
    height: 650px; /* 设置弹窗固定高度 */
    display: flex;
    flex-direction: column;

    .form-content {
      flex: 1;
      overflow-y: auto; /* 添加滚动条 */
      padding-right: 10px; /* 为滚动条留出空间 */
      margin-bottom: 20px;

      /* 隐藏滚动条 */
      &::-webkit-scrollbar {
        width: 0;
        display: none;
      }

      /* Firefox 隐藏滚动条 */
      scrollbar-width: none;

      /* IE 隐藏滚动条 */
      -ms-overflow-style: none;
    }

    .upload-section {
      margin-top: 10px;

      .upload-area {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        text-align: center;
        background: #fafafa;
        cursor: pointer;
        transition: border-color 0.3s;

        .upload-placeholder {
          color: #8c939d;
          font-size: 14px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
            display: block;
          }
        }
      }

      .image-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;

        .image-item {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e4e7ed;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .image-actions {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
              background: rgba(0, 0, 0, 0.8);
            }

            i {
              color: #fff;
              font-size: 12px;
            }
          }
        }
      }
    }

    .form-actions {
      flex-shrink: 0; /* 防止按钮区域被压缩 */
      padding-top: 20px;
      text-align: center;
      border-top: 1px solid #e4e7ed;
      background: #fff;
    }
  }

  .delete-confirm {
    padding: 0 20px 20px 20px;
    text-align: center;

    .confirm-content {
      margin-bottom: 20px;

      p {
        margin: 8px 0;
        color: #606266;
        font-size: 14px;
      }
    }

    .confirm-actions {
      display: flex;
      justify-content: center;
      gap: 12px;
    }
  }
}

</style>
